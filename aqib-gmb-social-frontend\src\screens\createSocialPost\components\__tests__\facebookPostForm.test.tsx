import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import { vi } from "vitest";
import FacebookPostForm from "../facebookPostForm.component";
import { IFacebookCreatePost } from "../../../../interfaces/request/IFacebookCreatePost";

// Mock the form data
const mockFormData: IFacebookCreatePost = {
  pageId: "",
  message: "",
  link: "",
  media: [],
  published: true,
};

const mockProps = {
  formData: mockFormData,
  onFormChange: vi.fn(),
  uploadedImages: [],
  onImageUpload: vi.fn(),
  onGalleryOpen: vi.fn(),
  onImageRemove: vi.fn(),
  errors: {},
  onSubmit: vi.fn(),
  isFacebookConnected: true,
};

describe("FacebookPostForm", () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  test("renders Facebook post form without page selector", () => {
    render(<FacebookPostForm {...mockProps} />);

    // Check that the message input is present
    expect(
      screen.getByPlaceholderText(
        "Write details about what's new with your business"
      )
    ).toBeInTheDocument();

    // Check that the link input is present
    expect(
      screen.getByPlaceholderText("https://example.com")
    ).toBeInTheDocument();

    // Check that the submit button is present
    expect(screen.getByText("Create Facebook Post")).toBeInTheDocument();

    // Check that upload buttons are present
    expect(screen.getByText("Upload from Computer")).toBeInTheDocument();
    expect(screen.getByText("Select from Gallery")).toBeInTheDocument();

    // Verify that Facebook page selector is NOT present in the form
    expect(screen.queryByText("Select Facebook Pages")).not.toBeInTheDocument();
    expect(screen.queryByText("Facebook Pages")).not.toBeInTheDocument();
  });

  test("shows placeholder text with page name replacement hint", () => {
    render(<FacebookPostForm {...mockProps} />);

    const messageInput = screen.getByPlaceholderText(
      "Write details about what's new with your business"
    );
    expect(messageInput).toBeInTheDocument();
  });

  test("shows helper text with character count", () => {
    render(<FacebookPostForm {...mockProps} />);

    expect(screen.getByText(/0\/1500 characters/)).toBeInTheDocument();
  });

  test("submit button is disabled when message is empty", () => {
    render(<FacebookPostForm {...mockProps} />);

    const submitButton = screen.getByText("Create Facebook Post");
    expect(submitButton).toBeDisabled();
  });

  test("submit button is enabled when message is provided", () => {
    const formDataWithMessage = {
      ...mockFormData,
      message: "Test message",
    };

    render(<FacebookPostForm {...mockProps} formData={formDataWithMessage} />);

    const submitButton = screen.getByText("Create Facebook Post");
    expect(submitButton).not.toBeDisabled();
  });

  test("calls onSubmit when submit button is clicked", () => {
    const formDataWithMessage = {
      ...mockFormData,
      message: "Test message",
    };

    render(<FacebookPostForm {...mockProps} formData={formDataWithMessage} />);

    const submitButton = screen.getByText("Create Facebook Post");
    fireEvent.click(submitButton);

    expect(mockProps.onSubmit).toHaveBeenCalledTimes(1);
  });

  test("shows page name placeholder button", () => {
    render(<FacebookPostForm {...mockProps} />);

    // Check that the page name placeholder button is present
    const placeholderButton = screen.getByTitle("Add {Page Name} placeholder");
    expect(placeholderButton).toBeInTheDocument();
  });

  test("adds page name placeholder when button is clicked", () => {
    const mockOnFormChange = vi.fn();
    const formDataWithMessage = {
      ...mockFormData,
      message: "Hello",
    };

    render(
      <FacebookPostForm
        {...mockProps}
        formData={formDataWithMessage}
        onFormChange={mockOnFormChange}
      />
    );

    const placeholderButton = screen.getByTitle("Add {Page Name} placeholder");
    fireEvent.click(placeholderButton);

    expect(mockOnFormChange).toHaveBeenCalledWith({
      ...formDataWithMessage,
      message: "Hello {Page Name}",
    });
  });

  test("shows link input field", () => {
    render(<FacebookPostForm {...mockProps} />);

    expect(
      screen.getByPlaceholderText("https://example.com")
    ).toBeInTheDocument();
  });

  test("shows media upload section", () => {
    render(<FacebookPostForm {...mockProps} />);

    expect(screen.getByText("Add/Edit Post Image")).toBeInTheDocument();
    expect(screen.getByText("Upload from Computer")).toBeInTheDocument();
    expect(screen.getByText("Select from Gallery")).toBeInTheDocument();
    expect(screen.getByText(/Maximum Size Photo/)).toBeInTheDocument();
  });
});
