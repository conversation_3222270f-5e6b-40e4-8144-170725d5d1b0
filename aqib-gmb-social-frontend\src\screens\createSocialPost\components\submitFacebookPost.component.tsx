import React, { useState } from "react";
import {
  <PERSON>ton,
  <PERSON>po<PERSON>,
  Checkbox,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  FormControlLabel,
  Box,
  Avatar,
  Badge,
  Chip,
} from "@mui/material";
import { Formik } from "formik";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import {
  IFacebookCreatePost,
  IFacebookSelectedPage,
} from "../../../interfaces/request/IFacebookCreatePost";
import { IFacebookPageData } from "../../../interfaces/response/IFacebookCreatePostResponse";

export interface IFacebookPageSelectionModal {
  isShow: boolean;
  closeModal?: () => void;
  facebookFormData?: IFacebookCreatePost;
  uploadedImages?: any[];
  facebookPages: IFacebookPageData[];
  onSubmitPosts?: (selectedPages: IFacebookSelectedPage[]) => Promise<void>;
}

const SubmitFacebookPost = (props: IFacebookPageSelectionModal) => {
  const { closeModal, facebookPages, onSubmitPosts } = props;
  const [selectedPages, setSelectedPages] = useState<IFacebookSelectedPage[]>(
    []
  );

  const INITIAL_VALUES = {};

  const handlePageToggle = (page: IFacebookPageData, isSelected: boolean) => {
    if (isSelected) {
      // Add page to selection
      const newSelectedPage: IFacebookSelectedPage = {
        pageId: page.page_id,
        pageName: page.page_name,
        pageCategory: page.page_category,
        pagePictureUrl: page.page_picture_url,
        status: undefined,
        facebookUrl: undefined,
        error: undefined,
      };
      setSelectedPages([...selectedPages, newSelectedPage]);
    } else {
      // Remove page from selection
      setSelectedPages(selectedPages.filter((p) => p.pageId !== page.page_id));
    }
  };

  const isPageSelected = (pageId: string) => {
    return selectedPages.some((p) => p.pageId === pageId);
  };

  const handleSelectAll = (isSelectAll: boolean) => {
    if (isSelectAll) {
      const allPages: IFacebookSelectedPage[] = facebookPages.map((page) => ({
        pageId: page.page_id,
        pageName: page.page_name,
        pageCategory: page.page_category,
        pagePictureUrl: page.page_picture_url,
        status: undefined,
        facebookUrl: undefined,
        error: undefined,
      }));
      setSelectedPages(allPages);
    } else {
      setSelectedPages([]);
    }
  };

  const _handleCreatePosts = async (values: any, formikHelpers: any) => {
    if (selectedPages.length > 0 && onSubmitPosts) {
      await onSubmitPosts(selectedPages);
    }
    formikHelpers.setSubmitting(false);
  };

  return (
    <Formik
      enableReinitialize={true}
      initialValues={INITIAL_VALUES}
      onSubmit={_handleCreatePosts}
    >
      {({ handleSubmit, isSubmitting }) => (
        <form onSubmit={handleSubmit} className="commonModal">
          <Box className="height100">
            <Typography
              id="modal-modal-title"
              variant="h6"
              component="h2"
              className="modal-modal-title"
            >
              Select Facebook Pages to Create Post
            </Typography>

            <Box
              id="modal-modal-description"
              className="modal-modal-description"
            >
              <Box>
                {/* Select All Checkbox */}
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={
                        selectedPages.length === facebookPages.length &&
                        facebookPages.length > 0
                      }
                      indeterminate={
                        selectedPages.length > 0 &&
                        selectedPages.length < facebookPages.length
                      }
                      onChange={(e) => handleSelectAll(e.target.checked)}
                    />
                  }
                  label={`Select All Pages (${selectedPages.length}/${facebookPages.length})`}
                />

                {/* Facebook Pages Table */}
                <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
                  <Table stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell padding="checkbox">Select</TableCell>
                        <TableCell>Page</TableCell>
                        <TableCell>Category</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {facebookPages.map((page) => (
                        <TableRow key={page.page_id}>
                          <TableCell padding="checkbox">
                            <Checkbox
                              checked={isPageSelected(page.page_id)}
                              onChange={(e) =>
                                handlePageToggle(page, e.target.checked)
                              }
                            />
                          </TableCell>
                          <TableCell>
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "center",
                                gap: 1,
                              }}
                            >
                              {page.page_picture_url ? (
                                <Badge
                                  overlap="circular"
                                  anchorOrigin={{
                                    vertical: "top",
                                    horizontal: "right",
                                  }}
                                  badgeContent={
                                    isPageSelected(page.page_id) ? (
                                      <CheckCircleIcon
                                        sx={{
                                          color: "primary.main",
                                          fontSize: 16,
                                          backgroundColor: "white",
                                          borderRadius: "50%",
                                        }}
                                      />
                                    ) : null
                                  }
                                >
                                  <Avatar
                                    src={page.page_picture_url}
                                    sx={{ width: 32, height: 32 }}
                                  />
                                </Badge>
                              ) : (
                                <Avatar sx={{ width: 32, height: 32 }}>
                                  {page.page_name.charAt(0).toUpperCase()}
                                </Avatar>
                              )}
                              <Typography variant="body2">
                                {page.page_name}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2" color="text.secondary">
                              {page.page_category || "N/A"}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {selectedPages.length === 0 && (
                  <Typography
                    color="error"
                    variant="caption"
                    sx={{ mt: 1, display: "block" }}
                  >
                    Please select at least one Facebook page
                  </Typography>
                )}
              </Box>
            </Box>

            {/* Action Buttons */}
            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                p: 2,
                borderTop: "1px solid #e0e0e0",
              }}
            >
              <Button
                variant="outlined"
                onClick={closeModal}
                disabled={isSubmitting}
                className="updatesShapeBtn"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                disabled={selectedPages.length === 0 || isSubmitting}
                className="updatesShapeBtn"
                style={{ textTransform: "capitalize" }}
              >
                {selectedPages.length > 1
                  ? `Create Posts for ${selectedPages.length} Pages`
                  : "Create Facebook Post"}
              </Button>
            </Box>
          </Box>
        </form>
      )}
    </Formik>
  );
};

export default SubmitFacebookPost;
