import {
  createSvgIcon
} from "./chunk-S36MRKJE.js";
import {
  require_jsx_runtime
} from "./chunk-YG5AX2YU.js";
import {
  __toESM
} from "./chunk-2TUXWMP5.js";

// node_modules/@mui/icons-material/esm/Publish.js
var import_jsx_runtime = __toESM(require_jsx_runtime());
var Publish_default = createSvgIcon((0, import_jsx_runtime.jsx)("path", {
  d: "M5 4v2h14V4zm0 10h4v6h6v-6h4l-7-7z"
}), "Publish");

export {
  Publish_default
};
//# sourceMappingURL=chunk-A3P756LX.js.map
