{"version": 3, "sources": ["../../@mui/icons-material/esm/CategoryRounded.js"], "sourcesContent": ["\"use client\";\n\nimport createSvgIcon from \"./utils/createSvgIcon.js\";\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default createSvgIcon([/*#__PURE__*/_jsx(\"path\", {\n  d: \"M11.15 3.4 7.43 9.48c-.41.66.07 1.52.85 1.52h7.43c.78 0 1.26-.86.85-1.52L12.85 3.4c-.39-.64-1.31-.64-1.7 0\"\n}, \"0\"), /*#__PURE__*/_jsx(\"circle\", {\n  cx: \"17.5\",\n  cy: \"17.5\",\n  r: \"4.5\"\n}, \"1\"), /*#__PURE__*/_jsx(\"path\", {\n  d: \"M4 21.5h6c.55 0 1-.45 1-1v-6c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v6c0 .55.45 1 1 1\"\n}, \"2\")], 'CategoryRounded');"], "mappings": ";;;;;;;;;;;AAGA,yBAA4B;AAC5B,IAAO,0BAAQ,cAAc,KAAc,mBAAAA,KAAK,QAAQ;AAAA,EACtD,GAAG;AACL,GAAG,GAAG,OAAgB,mBAAAA,KAAK,UAAU;AAAA,EACnC,IAAI;AAAA,EACJ,IAAI;AAAA,EACJ,GAAG;AACL,GAAG,GAAG,OAAgB,mBAAAA,KAAK,QAAQ;AAAA,EACjC,GAAG;AACL,GAAG,GAAG,CAAC,GAAG,iBAAiB;", "names": ["_jsx"]}