import React, {
  FunctionComponent,
  useContext,
  useEffect,
  useState,
} from "react";
import PageProps from "../../../models/PageProps.interface";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import TextField from "@mui/material/TextField";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import { Drawer, Grid2, Pagination, Tooltip } from "@mui/material";
import DriveFileRenameOutlineIcon from "@mui/icons-material/DriveFileRenameOutline";
import DeleteOutlineIcon from "@mui/icons-material/DeleteOutline";
import BlockOutlinedIcon from "@mui/icons-material/BlockOutlined";
import CheckCircleRoundedIcon from "@mui/icons-material/CheckCircleRounded";
import LeftMenuComponent from "../../../components/leftMenu/leftMenu.component";
import "../manageBusiness/manageBusiness.screen.style.css";
import { useDispatch, useSelector } from "react-redux";
import { LoadingContext } from "../../../context/loading.context";
import {
  IBusiness,
  IBusinessListPaginatedResponse,
  IBusinessListResponseModel,
} from "../../../interfaces/response/IBusinessListResponseModel";
import BusinessService from "../../../services/business/business.service";
import AddEditBusinessComponent from "../../../components/addEditBusiness/addEditBusiness.component";
import { IBusinessCreationResponseModel } from "../../../interfaces/response/IBusinessCreationResponseModel";
import { ToastSeverity } from "../../../constants/toastSeverity.constant";
import { MessageConstants } from "../../../constants/message.constant";
import { ToastContext } from "../../../context/toast.context";
import { IAlertDialogConfig } from "../../../interfaces/IAlertDialogConfig";
import AlertDialog from "../../../components/alertDialog/alertDialog.component";
import ConfirmModel from "../../../components/confirmModel/confirmModel.component";
import { IUserResponseModel } from "../../../interfaces/response/IUserResponseModel";
import { IAddBusinessRequestModel } from "../../../interfaces/request/IAddBusinessRequestModel";
import Switch from "@mui/material/Switch";
import FormGroup from "@mui/material/FormGroup";
import FormControlLabel from "@mui/material/FormControlLabel";
import { IPaginationModel } from "../../../interfaces/IPaginationModel";
import {
  DEFAULT_PAGINATION,
  RoleType,
} from "../../../constants/dbConstant.constant";
import TableRowsRoundedIcon from "@mui/icons-material/TableRowsRounded";
import SyncOutlinedIcon from "@mui/icons-material/SyncOutlined";
//Icons
import { IPaginationResponseModel } from "../../../interfaces/IPaginationResponseModel";
import ApplicationHelperService from "../../../services/ApplicationHelperService";
import GenericDrawer from "../../../components/genericDrawer/genericDrawer.component";
import { IconButton } from "@mui/material";
import AuthService from "../../../services/auth/auth.service";
import VerifiedIcon from "@mui/icons-material/Verified";
import CancelIcon from "@mui/icons-material/Cancel";
import PauseCircleFilledIcon from "@mui/icons-material/PauseCircleFilled";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import SearchRoundedIcon from "@mui/icons-material/SearchRounded";
import AddOutlinedIcon from "@mui/icons-material/AddOutlined";
import NoRowsFound from "../../../components/noRowsFound/noRowsFound.component";

// Image imports
import googleImage from "../../../assets/common/google.png?url";
import facebookImage from "../../../assets/common/facebook.jpg?url";
import instagramImage from "../../../assets/common/instagram.png?url";

type IDeleteRecord = {
  isShow: boolean;
  data: any;
  businessId: number;
};

const ManageBusiness: FunctionComponent<PageProps> = ({ title }) => {
  const dispatch = useDispatch();
  const [openAddEdit, setOpenAddEdit] = React.useState<IDeleteRecord>({
    isShow: false,
    data: null,
    businessId: 0,
  });

  const [showConfirmPopup, setShowConfirmPopup] = useState<IDeleteRecord>({
    isShow: false,
    data: null,
    businessId: 0,
  });
  const { setToastConfig, setOpen } = useContext(ToastContext);
  const _businessService = new BusinessService(dispatch);
  const _authService = new AuthService(dispatch);
  const _applicationHelperService = new ApplicationHelperService({});
  const { userInfo, rbAccess } = useSelector((state: any) => state.authReducer);
  const { setLoading } = useContext(LoadingContext);
  const [businessList, setBusinessList] = useState<IBusiness[]>([]);
  const [allData, setAllData] = useState<IBusiness[]>([]);
  const [paginationModel, setPaginationModel] =
    useState<IPaginationModel>(DEFAULT_PAGINATION);
  const [paginationResponseModel, setPaginationResponseModel] =
    useState<IPaginationResponseModel>();
  const [alertConfig, setAlertConfig] = useState<IAlertDialogConfig>({
    isShow: false,
    callBack: () => undefined,
  });
  const [searchText, setSearchText] = useState("");

  useEffect(() => {
    document.title = `${title}`;
    fetchBusinessPaginated();
    console.log(userInfo);
  }, []);

  const fetchBusinessPaginated = async () => {
    try {
      setLoading(true);
      const businessListResponse: IBusinessListPaginatedResponse =
        await _businessService.getBusinessPaginated(
          userInfo.id,
          paginationModel
        );
      console.log("Business List: ", businessListResponse.results);
      setBusinessList(businessListResponse.results);
      setAllData(businessListResponse.results);
      setPaginationResponseModel({ ...businessListResponse.pagination });
    } catch (error) {}

    setLoading(false);
  };

  const deleteRecord = async () => {
    try {
      setShowConfirmPopup({ isShow: false, data: null, businessId: 0 });
      setLoading(true);
      if (showConfirmPopup.data) {
        const response: IUserResponseModel =
          await _businessService.deleteBusniess(
            (showConfirmPopup.data as IBusiness).id
          );
        if (response.list.affectedRows > 0) {
          setToastConfig(
            ToastSeverity.Success,
            MessageConstants.BusinessDeletedSuccessfully,
            true
          );
        } else {
          setToastConfig(
            ToastSeverity.Error,
            MessageConstants.BusinessDeletionFailed,
            true
          );
        }
        fetchBusinessPaginated();
      }
    } catch (error) {}

    setLoading(false);
  };

  const enableDisableBusiness = async (id: number, checked: boolean) => {
    try {
      setLoading(true);
      const response: IUserResponseModel =
        await _businessService.enableDisableBusniess(id, {
          isActive: Number(checked),
        });
      if (response.list.affectedRows > 0) {
        setToastConfig(
          ToastSeverity.Success,
          MessageConstants.BusinessUpdatedSuccessfully,
          true
        );
      } else {
        setToastConfig(
          ToastSeverity.Error,
          MessageConstants.BusinessUpdateFailed,
          true
        );
      }
      fetchBusinessPaginated();
    } catch (error) {}

    setLoading(false);
  };

  const handleGoogleAuth = async (data: IBusiness) => {
    const formData = {
      businessId: data.id,
      businessEmail: data.businessEmail,
    };
    try {
      const response = await _authService.googleAuthenticate(formData);
      const authorizeUrl = response.authorizeUrl;
      window.location.href = authorizeUrl;
    } catch (error) {
      console.error("Authentication failed:", error);
    }
  };

  const handleSearch = (e: any) => {
    const value = e.target.value;
    setSearchText(value);

    if (value.trim() === "") {
      // Show original data if search is cleared
      setBusinessList(allData);
    } else {
      // Filter data
      const filtered = allData.filter((item) =>
        item.businessName.toLowerCase().includes(value.toLowerCase())
      );
      setBusinessList(filtered);
    }
  };

  return (
    <div>
      <Box>
        <Box>
          <LeftMenuComponent>
            <Box>
              <h3 className="pageTitle">Business Management</h3>
              <Box className="commonTableHeader">
                <Box>
                  <Box className="searchInput">
                    <TextField
                      hiddenLabel
                      id="filled-hidden-label-small"
                      defaultValue=""
                      placeholder="Search Business"
                      variant="filled"
                      size="small"
                      onChange={handleSearch}
                    />
                    <span className="placeHolderIcon">
                      <SearchRoundedIcon />
                    </span>
                  </Box>
                </Box>
                <Box sx={{ marginTop: 1, marginBottom: 2 }}>
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    sx={{ fontStyle: "italic" }}
                  >
                    Note: Modifying groups in the Google interface necessitates
                    resyncing the data.
                  </Typography>
                </Box>
                {Boolean(rbAccess && rbAccess.BusinessCreate) && (
                  <Button
                    onClick={() =>
                      setOpenAddEdit({
                        isShow: true,
                        data: null,
                        businessId: 0,
                      })
                    }
                    className="tableActionBtn"
                    sx={{
                      minHeight: "50px", // Set the desired height
                    }}
                    startIcon={<AddOutlinedIcon />}
                  >
                    <span className="responsiveHide">Add Business</span>
                  </Button>
                )}
              </Box>
              <Box>
                <TableContainer className="commonTable">
                  <Table aria-label="simple table">
                    <TableHead>
                      <TableRow>
                        <TableCell>Business Name</TableCell>
                        <TableCell>Business Email</TableCell>
                        <TableCell>Status</TableCell>
                        {userInfo &&
                          userInfo.roleId &&
                          userInfo.roleId !== RoleType.User && (
                            <TableCell>Actions</TableCell>
                          )}

                        <TableCell>Authenticate</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {businessList.length > 0 ? (
                        businessList.map(
                          (business: IBusiness, index: number) => (
                            <TableRow>
                              <TableCell scope="row" data-label="Business Name">
                                <Box>
                                  <Box>
                                    <Typography>
                                      {business.businessName}
                                    </Typography>
                                    <Typography className="badgeText">
                                      {business.name}
                                    </Typography>
                                  </Box>
                                </Box>
                              </TableCell>
                              <TableCell data-label="Business Email">
                                {business.businessEmail}
                              </TableCell>
                              <TableCell data-label="Status">
                                <FormControlLabel
                                  control={
                                    <Switch
                                      disabled={
                                        !Boolean(
                                          rbAccess && rbAccess.BusinessEdit
                                        )
                                      }
                                      checked={Boolean(business.isActive)}
                                      onChange={(
                                        event: React.ChangeEvent<HTMLInputElement>,
                                        checked: boolean
                                      ) => {
                                        enableDisableBusiness(
                                          business.id,
                                          checked
                                        );
                                      }}
                                    />
                                  }
                                  label=""
                                />
                              </TableCell>
                              {userInfo &&
                                userInfo.roleId &&
                                userInfo.roleId !== RoleType.User && (
                                  <TableCell align="left" data-label="Actions">
                                    <Box className="commonTableActionBtns">
                                      <Box>
                                        <Button
                                          variant="text"
                                          className="emptyBtn"
                                          color="secondary"
                                          startIcon={
                                            <DriveFileRenameOutlineIcon />
                                          }
                                          onClick={() =>
                                            setOpenAddEdit({
                                              isShow: true,
                                              data: business,
                                              businessId: business.id,
                                            })
                                          }
                                          disabled={
                                            !Boolean(
                                              rbAccess && rbAccess.BusinessEdit
                                            )
                                          }
                                        ></Button>
                                      </Box>
                                      <Box>
                                        <Button
                                          variant="text"
                                          className="emptyBtn"
                                          color="error"
                                          startIcon={<DeleteOutlineIcon />}
                                          onClick={() =>
                                            setShowConfirmPopup({
                                              isShow: true,
                                              data: business,
                                              businessId: 0,
                                            })
                                          }
                                          disabled={
                                            !Boolean(
                                              rbAccess &&
                                                rbAccess.BusinessDelete
                                            )
                                          }
                                        ></Button>
                                      </Box>
                                    </Box>
                                  </TableCell>
                                )}

                              <TableCell data-label="Authenticate">
                                <Tooltip
                                  title={
                                    userInfo &&
                                    userInfo.roleId === RoleType.Admin
                                      ? "Data sync is not permitted for admin users."
                                      : ""
                                  }
                                  arrow
                                >
                                  <span>
                                    <IconButton
                                      onClick={() => handleGoogleAuth(business)}
                                      className="businessAuthenticate"
                                      disabled={
                                        userInfo &&
                                        userInfo.roleId === RoleType.Admin
                                      }
                                    >
                                      <img
                                        alt="MyLocoBiz - Google Business"
                                        className="googleBusiness"
                                        src={googleImage}
                                      />
                                      {userInfo &&
                                        userInfo.roleId !== RoleType.Admin && (
                                          <Box
                                            id="businessStatus"
                                            className="businessStatus success"
                                          >
                                            {Boolean(
                                              business.isGoogleSyncComplete
                                            ) ? (
                                              <SyncOutlinedIcon
                                                fontSize="small"
                                                color="success"
                                              />
                                            ) : (
                                              <CancelIcon
                                                color="error"
                                                fontSize="small"
                                              />
                                            )}
                                          </Box>
                                        )}
                                    </IconButton>
                                  </span>
                                </Tooltip>
                                {/* <IconButton
                                  onClick={() => handleGoogleAuth(business)}
                                  className="businessAuthenticate"
                                  disabled={true}
                                >
                                  <img
                                    alt="MyLocoBiz - Google Business"
                                    className="googleBusiness"
                                    src={facebookImage}
                                    style={{ width: 25 }}
                                  />
                                  <Box
                                    id="businessStatus"
                                    className="businessStatus success"
                                  >
                                    {Boolean(business.isGoogleSyncComplete) ? (
                                      <VerifiedIcon
                                        fontSize="small"
                                        color="success"
                                      />
                                    ) : (
                                      <PauseCircleFilledIcon
                                        fontSize="small"
                                        color="disabled"
                                      />
                                    )}
                                  </Box>
                                </IconButton>
                                <IconButton
                                  onClick={() => handleGoogleAuth(business)}
                                  className="businessAuthenticate"
                                  disabled={true}
                                  style={{ width: 25 }}
                                >
                                  <img
                                    alt="MyLocoBiz - Google Business"
                                    className="googleBusiness"
                                    src={instagramImage}
                                  />
                                  <Box
                                    id="businessStatus"
                                    className="businessStatus success"
                                  >
                                    {Boolean(business.isGoogleSyncComplete) ? (
                                      <VerifiedIcon
                                        fontSize="small"
                                        color="success"
                                      />
                                    ) : (
                                      <PauseCircleFilledIcon
                                        fontSize="small"
                                        color="disabled"
                                      />
                                    )}
                                  </Box>
                                </IconButton> */}
                                {/* Instagram Sync Button */}
                              </TableCell>
                            </TableRow>
                          )
                        )
                      ) : (
                        <NoRowsFound />
                      )}
                    </TableBody>
                  </Table>
                  <Grid2
                    spacing={0}
                    direction="row"
                    alignItems="center"
                    justifyContent="center"
                    columnSpacing={{ xs: 2, md: 3 }}
                    container
                    height={50}
                  >
                    <Grid2 size={6} justifyItems={"flex-start"}>
                      {paginationResponseModel && (
                        <Typography className="pagination-Text">
                          {_applicationHelperService.getPaginationText(
                            paginationModel.pageNo,
                            paginationModel.offset,
                            paginationResponseModel.totalRecords
                          )}
                        </Typography>
                      )}
                    </Grid2>
                    <Grid2 size={6} justifyItems={"flex-end"}>
                      <Pagination
                        color="primary"
                        count={paginationResponseModel?.pageCount}
                        page={paginationModel.pageNo}
                        onChange={(
                          event: React.ChangeEvent<unknown>,
                          page: number
                        ) =>
                          setPaginationModel({
                            ...paginationModel,
                            pageNo: page,
                          })
                        }
                      />
                    </Grid2>
                  </Grid2>
                </TableContainer>
              </Box>
            </Box>
          </LeftMenuComponent>
        </Box>
      </Box>

      {/* Modal */}
      <GenericDrawer
        component={
          <AddEditBusinessComponent
            businessId={openAddEdit.businessId}
            editData={openAddEdit.data}
            callBack={(
              request: IAddBusinessRequestModel | undefined,
              resp: IBusinessCreationResponseModel | undefined
            ) => {
              if (resp && resp.response.affectedRows > 0) {
                setToastConfig(
                  ToastSeverity.Success,
                  MessageConstants.BusinessCreatedSuccessfully,
                  true
                );
              }

              setOpenAddEdit({ isShow: false, data: null, businessId: 0 });
              fetchBusinessPaginated();
            }}
          />
        }
        isShow={openAddEdit.isShow}
        callback={() =>
          setOpenAddEdit({ isShow: false, data: null, businessId: 0 })
        }
      />

      {alertConfig && alertConfig.isShow && (
        <AlertDialog
          alertConfig={alertConfig}
          callBack={alertConfig.callBack}
        />
      )}

      {showConfirmPopup.data && (
        <ConfirmModel
          isOpen={showConfirmPopup.isShow}
          title="Delete User"
          description="Are you certain you want to delete this business? This action is irreversible."
          confirmText="Delete"
          cancelText="Cancel"
          cancelCallback={() =>
            setShowConfirmPopup({ isShow: false, data: null, businessId: 0 })
          }
          confirmCallback={() => deleteRecord()}
        />
      )}
    </div>
  );
};

export default ManageBusiness;
