const pool = require("../config/db");
const logger = require("../utils/logger");
const util = require("util");

// Create a promisified query function to ensure it works properly
const queryAsync = util.promisify(pool.query.bind(pool));

module.exports = class LocalFalcon {
  /**
   * Save Local Falcon configuration to database
   */
  static async saveConfiguration(configData) {
    try {
      const {
        userId,
        name,
        keyword,
        businessName,
        placeId,
        centerLat,
        centerLng,
        gridSize,
        radius,
        unit,
        isScheduleEnabled,
        scheduleFrequency,
        alertThreshold,
        settings,
      } = configData;

      const query = `
        INSERT INTO local_falcon_configurations
        (user_id, name, keyword, business_name, place_id, center_lat, center_lng, 
         grid_size, radius, unit, is_schedule_enabled, schedule_frequency, 
         alert_threshold, settings, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
      `;

      const values = [
        userId,
        name,
        keyword,
        businessName,
        placeId || null,
        centerLat,
        centerLng,
        gridSize,
        radius,
        unit,
        isScheduleEnabled || false,
        scheduleFrequency || null,
        alertThreshold || null,
        JSON.stringify(settings || {}),
      ];

      logger.info("Executing Local Falcon configuration save query", {
        query: query.replace(/\s+/g, " ").trim(),
        valuesCount: values.length,
        values: values,
      });

      const result = await queryAsync(query, values);

      logger.info("Query executed successfully", {
        resultType: typeof result,
        resultKeys: Object.keys(result || {}),
        insertId: result?.insertId,
      });

      logger.info("Local Falcon configuration saved successfully", {
        configId: result.insertId,
        userId,
        name,
      });

      return {
        id: result.insertId,
        ...configData,
      };
    } catch (error) {
      logger.error("Error saving Local Falcon configuration:", {
        error: error.message,
        stack: error.stack,
        configData,
      });
      throw error;
    }
  }

  /**
   * Get all configurations for a user
   */
  static async getConfigurations(userId) {
    try {
      const query = `
        SELECT id, user_id, name, keyword, business_name, place_id, center_lat, center_lng,
               grid_size, radius, unit, is_schedule_enabled, schedule_frequency,
               alert_threshold, settings, created_at, updated_at
        FROM local_falcon_configurations
        WHERE user_id = ?
        ORDER BY updated_at DESC
      `;

      const rows = await queryAsync(query, [userId]);

      logger.info("Raw configurations from database", {
        userId,
        rowCount: rows.length,
        sampleRow:
          rows.length > 0
            ? {
                id: rows[0].id,
                name: rows[0].name,
                settingsType: typeof rows[0].settings,
                settingsValue: rows[0].settings,
              }
            : null,
      });

      return rows.map((row) => ({
        ...row,
        settings:
          typeof row.settings === "string"
            ? JSON.parse(row.settings)
            : row.settings || {},
        centerLat: parseFloat(row.center_lat),
        centerLng: parseFloat(row.center_lng),
        radius: parseFloat(row.radius),
      }));
    } catch (error) {
      logger.error("Error getting Local Falcon configurations:", {
        error: error.message,
        stack: error.stack,
        userId,
      });
      throw error;
    }
  }

  /**
   * Get specific configuration by ID
   */
  static async getConfiguration(configId) {
    try {
      const query = `
        SELECT id, user_id, name, keyword, business_name, place_id, center_lat, center_lng,
               grid_size, radius, unit, is_schedule_enabled, schedule_frequency,
               alert_threshold, settings, created_at, updated_at
        FROM local_falcon_configurations
        WHERE id = ?
      `;

      const rows = await queryAsync(query, [configId]);

      if (rows.length === 0) {
        return null;
      }

      const config = rows[0];
      return {
        ...config,
        settings:
          typeof config.settings === "string"
            ? JSON.parse(config.settings)
            : config.settings || {},
        centerLat: parseFloat(config.center_lat),
        centerLng: parseFloat(config.center_lng),
        radius: parseFloat(config.radius),
      };
    } catch (error) {
      logger.error("Error getting Local Falcon configuration:", {
        error: error.message,
        stack: error.stack,
        configId,
      });
      throw error;
    }
  }

  /**
   * Update configuration
   */
  static async updateConfiguration(configId, updateData) {
    try {
      const {
        name,
        keyword,
        businessName,
        placeId,
        centerLat,
        centerLng,
        gridSize,
        radius,
        unit,
        isScheduleEnabled,
        scheduleFrequency,
        alertThreshold,
        settings,
      } = updateData;

      const query = `
        UPDATE local_falcon_configurations
        SET name = ?, keyword = ?, business_name = ?, place_id = ?, center_lat = ?, center_lng = ?,
            grid_size = ?, radius = ?, unit = ?, is_schedule_enabled = ?, schedule_frequency = ?,
            alert_threshold = ?, settings = ?, updated_at = NOW()
        WHERE id = ?
      `;

      const values = [
        name,
        keyword,
        businessName,
        placeId || null,
        centerLat,
        centerLng,
        gridSize,
        radius,
        unit,
        isScheduleEnabled || false,
        scheduleFrequency || null,
        alertThreshold || null,
        JSON.stringify(settings || {}),
        configId,
      ];

      const result = await queryAsync(query, values);

      logger.info("Local Falcon configuration updated successfully", {
        configId,
        affectedRows: result.affectedRows,
      });

      return result;
    } catch (error) {
      logger.error("Error updating Local Falcon configuration:", {
        error: error.message,
        stack: error.stack,
        configId,
        updateData,
      });
      throw error;
    }
  }

  /**
   * Delete configuration
   */
  static async deleteConfiguration(configId) {
    try {
      // Then delete the configuration
      const query = "DELETE FROM local_falcon_configurations WHERE id = ?";
      const result = await queryAsync(query, [configId]);

      logger.info("Local Falcon configuration deleted successfully", {
        configId,
        affectedRows: result.affectedRows,
      });

      return result;
    } catch (error) {
      logger.error("Error deleting Local Falcon configuration:", {
        error: error.message,
        stack: error.stack,
        configId,
      });
      throw error;
    }
  }
};
