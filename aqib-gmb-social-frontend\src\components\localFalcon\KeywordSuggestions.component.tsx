import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  TextField,
  Card,
  CardContent,
  Typo<PERSON>,
  <PERSON>,
  Stack,
  IconButton,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
} from "@mui/material";
import {
  Add as AddIcon,
  AutoAwesome as AutoAwesomeIcon,
  Close as CloseIcon,
} from "@mui/icons-material";

interface KeywordSuggestionsProps {
  value: string;
  onChange: (keyword: string) => void;
  businessName?: string;
  businessCategory?: string;
  location?: string;
  businessType?: string;
  onGenerateSuggestions?: (request: {
    businessName: string;
    businessCategory?: string;
    location?: string;
    businessType?: string;
  }) => Promise<{
    success: boolean;
    data?: { keywords: string[] };
    error?: string;
  }>;
  disabled?: boolean;
}

const KeywordSuggestions: React.FC<KeywordSuggestionsProps> = ({
  value,
  onChange,
  businessName,
  businessCategory,
  location,
  businessType,
  onGenerateSuggestions,
  disabled = false,
}) => {
  const [suggestedKeywords, setSuggestedKeywords] = useState<string[]>([]);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [lastGeneratedForBusiness, setLastGeneratedForBusiness] =
    useState<string>("");

  // Generate suggestions when business information is available
  const handleGenerateSuggestions = async () => {
    if (!businessName || !onGenerateSuggestions) return;

    try {
      setIsGenerating(true);
      setError(null);

      const response = await onGenerateSuggestions({
        businessName,
        businessCategory,
        location,
        businessType,
      });

      if (response.success && response.data?.keywords) {
        setSuggestedKeywords(response.data.keywords);
        setShowSuggestions(true);
        setLastGeneratedForBusiness(businessName); // Track which business this was generated for
      } else {
        setError(response.error || "Failed to generate keyword suggestions");
      }
    } catch (error: any) {
      console.error("Error generating keyword suggestions:", error);
      setError("Failed to generate keyword suggestions. Please try again.");
    } finally {
      setIsGenerating(false);
    }
  };

  // Auto-generate suggestions only when business name changes (not on other prop changes)
  useEffect(() => {
    if (
      businessName &&
      businessName.length > 3 &&
      onGenerateSuggestions &&
      !disabled &&
      businessName !== lastGeneratedForBusiness // Only generate if business actually changed
    ) {
      const timer = setTimeout(() => {
        handleGenerateSuggestions();
      }, 500); // Shorter debounce for immediate response

      return () => clearTimeout(timer);
    }
  }, [businessName]); // Only depend on businessName, not other props

  // Clear suggestions when business name is cleared or changed to a different business
  useEffect(() => {
    if (!businessName || businessName.length < 3) {
      setShowSuggestions(false);
      setSuggestedKeywords([]);
      setLastGeneratedForBusiness("");
    } else if (
      businessName !== lastGeneratedForBusiness &&
      lastGeneratedForBusiness !== ""
    ) {
      // Business changed to a different one, clear old suggestions
      setShowSuggestions(false);
      setSuggestedKeywords([]);
    }
  }, [businessName, lastGeneratedForBusiness]);

  const handleKeywordSelect = (keyword: string) => {
    onChange(keyword);
    // Keep suggestions open after selection - don't call setShowSuggestions(false)
  };

  const canGenerateSuggestions =
    businessName &&
    businessName.length > 3 &&
    onGenerateSuggestions &&
    !disabled;

  return (
    <Box>
      {/* Keyword Input */}
      <TextField
        fullWidth
        label="Enter Keywords"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="e.g., restaurant, dentist, plumber"
        disabled={disabled}
        InputProps={{
          endAdornment: canGenerateSuggestions && (
            <IconButton
              onClick={handleGenerateSuggestions}
              disabled={isGenerating || disabled}
              size="small"
              color="primary"
              title="Generate AI keyword suggestions"
            >
              {isGenerating ? (
                <CircularProgress size={20} />
              ) : (
                <AutoAwesomeIcon />
              )}
            </IconButton>
          ),
        }}
        sx={{ mb: 1.5 }}
      />

      {/* Error Message */}
      {error && (
        <Alert severity="error" sx={{ mb: 1.5 }}>
          {error}
        </Alert>
      )}

      {/* No Keywords Message */}
      {!value && !showSuggestions && (
        <Card sx={{ mb: 1.5 }}>
          {/* <Typography variant="body2" color="text.secondary">
              {disabled
                ? "Please select a location first"
                : "No keywords entered"}
            </Typography> */}
          {canGenerateSuggestions && (
            <CardContent sx={{ textAlign: "center", py: 2 }}>
              <Button
                variant="outlined"
                startIcon={<AutoAwesomeIcon />}
                onClick={handleGenerateSuggestions}
                disabled={isGenerating}
                sx={{ mt: 1 }}
                size="small"
              >
                {isGenerating ? "Generating..." : "Generate AI Suggestions"}
              </Button>
            </CardContent>
          )}
        </Card>
      )}

      {/* Keyword Suggestions */}
      {showSuggestions && suggestedKeywords.length > 0 && (
        <Card sx={{ mb: 2 }}>
          <CardContent sx={{ p: 1.5, "&:last-child": { pb: 1.5 } }}>
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "space-between",
                mb: 1.5,
              }}
            >
              <Typography
                variant="subtitle2"
                color="primary"
                sx={{ textTransform: "none" }}
              >
                <AutoAwesomeIcon
                  sx={{ fontSize: 16, mr: 1, verticalAlign: "middle" }}
                />
                Suggested Keywords:
              </Typography>
              <IconButton
                size="small"
                onClick={() => setShowSuggestions(false)}
                title="Close suggestions"
              >
                <CloseIcon fontSize="small" />
              </IconButton>
            </Box>

            <Box
              sx={{
                display: "flex",
                flexWrap: "wrap",
                gap: 1,
                alignItems: "flex-start",
              }}
            >
              {suggestedKeywords.map((keyword, index) => (
                <Chip
                  key={index}
                  label={keyword}
                  onClick={() => handleKeywordSelect(keyword)}
                  onDelete={() => handleKeywordSelect(keyword)}
                  deleteIcon={<AddIcon />}
                  variant="outlined"
                  color="primary"
                  size="small"
                  sx={{
                    "&:hover": {
                      backgroundColor: "primary.light",
                      color: "primary.contrastText",
                    },
                    cursor: "pointer",
                    flexShrink: 0,
                  }}
                />
              ))}
            </Box>

            <Divider sx={{ my: 1.5 }} />

            <Box
              sx={{
                display: "flex",
                justifyContent: "space-between",
                alignItems: "center",
              }}
            >
              <Typography variant="caption" color="text.secondary">
                Click on any keyword to use it
              </Typography>
              <Button
                size="small"
                startIcon={<AutoAwesomeIcon />}
                onClick={handleGenerateSuggestions}
                disabled={isGenerating}
                variant="outlined"
                sx={{ textTransform: "none" }}
              >
                {isGenerating ? "Generating..." : "Regenerate"}
              </Button>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default KeywordSuggestions;
