import React, { useState } from "react";
import {
  Box,
  TextField,
  FormControlLabel,
  Switch,
  <PERSON>ton,
  <PERSON>pography,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  InputAdornment,
} from "@mui/material";
import { LocalizationProvider, DateTimePicker } from "@mui/x-date-pickers";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs, { Dayjs } from "dayjs";
import DeleteIcon from "@mui/icons-material/Delete";
import CloudUploadIcon from "@mui/icons-material/CloudUpload";
import PhotoLibraryIcon from "@mui/icons-material/PhotoLibrary";
import AutoFixHighIcon from "@mui/icons-material/AutoFixHigh";
import {
  IFacebookCreatePost,
  IFacebookSelectedPage,
} from "../../../interfaces/request/IFacebookCreatePost";
import { IFacebookPageData } from "../../../interfaces/response/IFacebookCreatePostResponse";

interface FacebookPostFormProps {
  formData: IFacebookCreatePost;
  onFormChange: (data: IFacebookCreatePost) => void;
  uploadedImages: any[];
  onImageUpload: () => void;
  onGalleryOpen: () => void;
  onImageRemove: (index: number) => void;
  errors?: any;
  onSubmit?: () => void;
  isFacebookConnected?: boolean;
}

const FacebookPostForm: React.FC<FacebookPostFormProps> = ({
  formData,
  onFormChange,
  uploadedImages,
  onImageUpload,
  onGalleryOpen,
  onImageRemove,
  errors,
  onSubmit,
  isFacebookConnected = true,
}) => {
  const [scheduleForLater, setScheduleForLater] = useState(false);
  const [scheduledDate, setScheduledDate] = useState<Dayjs | null>(
    dayjs().add(1, "hour")
  );

  const handleMessageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onFormChange({
      ...formData,
      message: event.target.value,
    });
  };

  const handleLinkChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    onFormChange({
      ...formData,
      link: event.target.value,
    });
  };

  const handleScheduleToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    const isScheduled = event.target.checked;
    setScheduleForLater(isScheduled);

    onFormChange({
      ...formData,
      published: !isScheduled,
      scheduledPublishTime:
        isScheduled && scheduledDate ? scheduledDate.toISOString() : undefined,
    });
  };

  const handleDateChange = (newDate: Dayjs | null) => {
    setScheduledDate(newDate);
    if (scheduleForLater && newDate) {
      onFormChange({
        ...formData,
        scheduledPublishTime: newDate.toISOString(),
      });
    }
  };

  // Helper function to add {Page Name} placeholder
  const addPageNamePlaceholder = () => {
    const currentValue = formData.message;
    const newValue = currentValue + (currentValue ? " " : "") + "{Page Name}";

    onFormChange({
      ...formData,
      message: newValue,
    });
  };

  return (
    <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
      {/* Summary/Message Card */}
      <Card sx={{ borderRadius: 2, boxShadow: 0 }}>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ position: "relative" }}>
            <TextField
              fullWidth
              multiline
              rows={6}
              label="Summary"
              variant="outlined"
              placeholder="Write details about what's new with your business"
              value={formData.message}
              onChange={handleMessageChange}
              error={!!errors?.message}
              sx={{
                "& .MuiOutlinedInput-root": {
                  backgroundColor: "transparent",
                  borderRadius: "8px",
                  "& fieldset": {
                    borderColor: "var(--primaryColor)",
                    borderWidth: "0.5px",
                  },
                  "&:hover fieldset": {
                    borderColor: "var(--primaryColor)",
                  },
                  "&.Mui-focused fieldset": {
                    borderColor: "var(--primaryColor)",
                    borderWidth: "0.5px",
                  },
                },
                "& .MuiInputBase-input": {
                  fontSize: "14px",
                  lineHeight: 1.5,
                },
              }}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="end">
                    <IconButton
                      onClick={() => addPageNamePlaceholder()}
                      size="small"
                      title="Add {Page Name} placeholder"
                      sx={{
                        color: "primary.main",
                        "&:hover": {
                          backgroundColor: "primary.light",
                          color: "white",
                        },
                      }}
                    >
                      <AutoFixHighIcon fontSize="small" />
                    </IconButton>
                  </InputAdornment>
                ),
              }}
            />
            <Typography
              variant="caption"
              sx={{
                color: "#666",
                mt: 1,
                display: "block",
                fontSize: "12px",
              }}
            >
              {errors?.message || `${formData.message.length}/1500 characters`}
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Link Card */}
      <Card sx={{ borderRadius: 2, boxShadow: 0 }}>
        <CardContent sx={{ p: 3 }}>
          <TextField
            fullWidth
            placeholder="https://example.com"
            label="Link (Optional)"
            value={formData.link || ""}
            onChange={handleLinkChange}
            variant="outlined"
            sx={{
              "& .MuiOutlinedInput-root": {
                backgroundColor: "transparent",
                borderRadius: "8px",
                "& fieldset": {
                  borderColor: "var(--primaryColor)",
                  borderWidth: "0.5px",
                },
                "&:hover fieldset": {
                  borderColor: "var(--primaryColor)",
                },
                "&.Mui-focused fieldset": {
                  borderColor: "var(--primaryColor)",
                  borderWidth: "0.5px",
                },
              },
              "& .MuiInputBase-input": {
                fontSize: "14px",
                lineHeight: 1.5,
              },
            }}
          />
        </CardContent>
      </Card>

      {/* Media Upload Card */}
      <Card sx={{ borderRadius: 2, boxShadow: 0 }}>
        <CardContent sx={{ p: 3, textAlign: "center" }}>
          <Box>
            <Box
              onClick={onGalleryOpen}
              sx={{
                border: "1px dashed #e0e0e0",
                borderRadius: 2,
                p: 4,
                textAlign: "center",
                cursor: "pointer",
                backgroundColor: "#fafafa",
                transition: "all 0.3s ease",
                "&:hover": {
                  borderColor: "var(--primaryColor)",
                  backgroundColor: "#f5f5f5",
                },
              }}
            >
              <CloudUploadIcon
                sx={{
                  fontSize: 48,
                  color: "#9e9e9e",
                  mb: 1,
                }}
              />
              <Typography variant="h6" sx={{ mb: 0.5, color: "#666" }}>
                Add/Edit Post Media
              </Typography>
              <Typography variant="caption" sx={{ color: "#999" }}>
                Supported: Images (JPG, PNG, GIF, WebP) and Videos (MP4, AVI,
                MOV, WMV, FLV, WebM)
              </Typography>
            </Box>
          </Box>
        </CardContent>
      </Card>

      {/* Schedule Card */}
      {/* <Card sx={{ border: "2px solid #e0e0e0", borderRadius: 2 }}>
        <CardContent sx={{ p: 3 }}>
          <Box sx={{ display: "flex", alignItems: "center", mb: 2 }}>
            <Typography
              variant="h6"
              sx={{
                color: "#1976d2",
                fontWeight: 600,
                fontSize: "16px",
              }}
            >
              Schedule Options
            </Typography>
          </Box>
          <FormControlLabel
            control={
              <Switch
                checked={scheduleForLater}
                onChange={handleScheduleToggle}
                color="primary"
              />
            }
            label="Schedule for later"
            sx={{ mb: scheduleForLater ? 2 : 0 }}
          />

          {scheduleForLater && (
            <LocalizationProvider dateAdapter={AdapterDayjs}>
              <DateTimePicker
                label="Schedule Date & Time"
                value={scheduledDate}
                onChange={handleDateChange}
                minDateTime={dayjs()}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    variant: "outlined",
                    sx: {
                      "& .MuiOutlinedInput-root": {
                        backgroundColor: "transparent",
                        borderRadius: "8px",
                        "& fieldset": {
                          borderColor: "#1976d2",
                          borderWidth: "2px",
                        },
                        "&:hover fieldset": {
                          borderColor: "#1976d2",
                        },
                        "&.Mui-focused fieldset": {
                          borderColor: "#1976d2",
                        },
                      },
                    },
                  },
                }}
              />
            </LocalizationProvider>
          )}
        </CardContent>
      </Card> */}

      {/* Submit Button */}
      {onSubmit && isFacebookConnected && (
        <Box sx={{ mt: 2 }}>
          <Button
            className="updatesShapeBtn"
            onClick={onSubmit}
            variant="contained"
            style={{ textTransform: "capitalize" }}
            fullWidth
            disabled={!formData.message}
            sx={{
              py: 1.5,
              fontSize: "16px",
              fontWeight: 600,
              borderRadius: 2,
            }}
          >
            Create Facebook Post
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default FacebookPostForm;
