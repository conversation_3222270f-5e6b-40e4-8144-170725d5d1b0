## Please note that all environment variables should have the prefix 'APP' for their names. ##

# ################# REQUIRED ENV VARS START #################
PREFIX=APP
APP_PORT=3000
APP_ENV_NAME=PRODUCTION
APP_VER_PREFIX=v1
APP_LOG_LEVEL=ERROR
# ################# REQUIRED ENV VARS END #################

# ################# DATABASE ENV VARS START #################
# Update these with your production database credentials
APP_DB_HOST=your-production-db-host.amazonaws.com
APP_DB_USER=admin
APP_DB_PASSWORD=your-production-db-password
APP_DB_NAME=gmb_production
# ################# DATABASE ENV VARS END #################

# ################# AWS S3 ENV VARS START #################
# Update these with your production AWS credentials
APP_AWS_ACCESS_KEY_ID=your-production-aws-access-key
APP_AWS_SECRET_ACCESS_KEY=your-production-aws-secret-key
APP_AWS_REGION=us-east-1
APP_AWS_S3_BUCKET=gmb-social-assets-production

# S3 URL Configuration
# Set to 'true' to use public URLs instead of signed URLs (requires public bucket or proper bucket policy)
S3_USE_PUBLIC_URLS=false
# ################# AWS S3 ENV VARS END #################

APP_JWT_SECRET_KEY=production-super-secure-secret-key

APP_GOOGLE_GENAI_KEY=your-production-google-genai-key

APP_PAGINATION_MAX_LIMIT = 5

# ################# FACEBOOK INTEGRATION ENV VARS START #################
# Facebook App Configuration (production app credentials)
FACEBOOK_CLIENT_ID=your-production-facebook-client-id
FACEBOOK_CLIENT_SECRET=your-production-facebook-client-secret
FACEBOOK_REDIRECT_URI=https://your-production-domain.com/v1/facebook/callback

# Frontend URL for redirects after authentication
FRONTEND_URL=https://your-production-frontend-domain.com
UI_ORIGIN=https://your-production-frontend-domain.com

# Facebook API Version (using latest v20.0)
FACEBOOK_API_VERSION=v20.0
# ################# FACEBOOK INTEGRATION ENV VARS END #################

# ################# INSTAGRAM INTEGRATION ENV VARS START #################
# Instagram App Configuration (uses Facebook app credentials for Instagram Basic Display API)
INSTAGRAM_CLIENT_ID=your-production-instagram-client-id
INSTAGRAM_CLIENT_SECRET=your-production-instagram-client-secret
INSTAGRAM_REDIRECT_URI=https://your-production-domain.com/v1/instagram/callback

# Instagram API Version (using latest v20.0)
INSTAGRAM_API_VERSION=v20.0
# ################# INSTAGRAM INTEGRATION ENV VARS END #################

# ################# LINKEDIN INTEGRATION ENV VARS START #################
# LinkedIn App Configuration
LINKEDIN_CLIENT_ID=your-production-linkedin-client-id
LINKEDIN_CLIENT_SECRET=your-production-linkedin-client-secret
LINKEDIN_REDIRECT_URI=https://your-production-domain.com/v1/linkedin/callback

# LinkedIn API Version
LINKEDIN_API_VERSION=v2
# ################# LINKEDIN INTEGRATION ENV VARS END #################

# ################# TWITTER INTEGRATION ENV VARS START #################
# Twitter App Configuration (production app credentials)
TWITTER_CLIENT_ID=your-production-twitter-client-id
TWITTER_CLIENT_SECRET=your-production-twitter-client-secret
TWITTER_REDIRECT_URI=https://your-production-frontend-domain.com/business-management/twitter/callback

# Twitter API Version (using v2)
TWITTER_API_VERSION=v2
# ################# TWITTER INTEGRATION ENV VARS END #################

# ################# LOCAL FALCON INTEGRATION ENV VARS START #################
# Google Places API Configuration (for Local Falcon ranking checks)
GOOGLE_PLACES_API_KEY=your-production-google-places-api-key

# Local Falcon Configuration
LOCAL_FALCON_DEFAULT_RADIUS=5000
LOCAL_FALCON_MAX_RESULTS=25
LOCAL_FALCON_RATE_LIMIT_DELAY=100
# ################# LOCAL FALCON INTEGRATION ENV VARS END #################
