import { IPaginationModel } from "../interfaces/IPaginationModel";

export enum RoleType {
  Admin = 1,
  Manager = 2,
  User = 3,
}

export const DEFAULT_PAGINATION: IPaginationModel = {
  pageNo: 1,
  offset: 10,
};

export const STARRATINGMAP = {
  ONE: 1,
  TWO: 2,
  THREE: 3,
  FOUR: 4,
  FIVE: 5,
};

// Location Status Constants (matching backend)
export const LOCATION_STATUS = {
  ACTIVE: 1,
  TEMPORARILY_CLOSED: 2,
  PERMANENTLY_CLOSED: 3,
};

// Status display mapping
export const LOCATION_STATUS_DISPLAY = {
  [LOCATION_STATUS.ACTIVE]: "Active",
  [LOCATION_STATUS.TEMPORARILY_CLOSED]: "Temporarily Closed",
  [LOCATION_STATUS.PERMANENTLY_CLOSED]: "Permanently Closed",
};

// Helper function to get status display text
export const getLocationStatusDisplay = (statusId: number): string => {
  return LOCATION_STATUS_DISPLAY[statusId] || "Unknown";
};
