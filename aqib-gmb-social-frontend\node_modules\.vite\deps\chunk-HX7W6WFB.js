import {
  defaultTheme_default
} from "./chunk-CUOK27OA.js";
import {
  identifier_default
} from "./chunk-UUHLHOPM.js";
import {
  useTheme_default
} from "./chunk-K5IKTFF4.js";
import {
  require_react
} from "./chunk-73THXJN7.js";
import {
  __toESM
} from "./chunk-2TUXWMP5.js";

// node_modules/@mui/material/styles/useTheme.js
var React = __toESM(require_react());
function useTheme() {
  const theme = useTheme_default(defaultTheme_default);
  if (true) {
    React.useDebugValue(theme);
  }
  return theme[identifier_default] || theme;
}

export {
  useTheme
};
//# sourceMappingURL=chunk-HX7W6WFB.js.map
